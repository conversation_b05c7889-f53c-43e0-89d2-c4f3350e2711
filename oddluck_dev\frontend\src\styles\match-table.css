/* 比赛表格组件样式 */
.match-table-container {
  width: 100%;
  height: 100%;
  background: var(--bg-primary, #ffffff);
  overflow-y: auto;
  position: relative;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

/* 空状态 */
.empty-state {
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  text-align: center;
  color: var(--text-secondary, #6b7280);
  width: auto;
  min-width: 300px;
  z-index: 10;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: var(--text-tertiary, #9ca3af);
}

.empty-state h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 500;
  color: var(--text-primary, #111827);
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* 加载状态 - 骨架屏 */
.loading-state {
  padding: 16px;
}

.skeleton-table {
  width: 100%;
}

.skeleton-header {
  height: 40px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  margin-bottom: 8px;
  border-radius: 4px;
}

.skeleton-row {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.skeleton-cell {
  height: 32px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
  flex: 1;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 内容区域 */
.matches-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  background: var(--bg-secondary, #f9fafb);
}

.content-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary, #111827);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.match-count {
  font-size: 12px;
  color: var(--text-secondary, #6b7280);
  background: var(--bg-tertiary, #f3f4f6);
  padding: 4px 8px;
  border-radius: 12px;
}

.sort-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 10px;
  font-size: 12px;
  background: var(--bg-primary, #ffffff);
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 6px;
  color: var(--text-secondary, #6b7280);
  cursor: pointer;
  transition: all 0.2s ease;
}

.sort-button:hover {
  background: var(--bg-hover, #f9fafb);
  border-color: var(--border-hover, #d1d5db);
  color: var(--text-primary, #111827);
}

.sort-icon {
  font-size: 10px;
}

/* 表格容器 */
.table-container {
  flex: 1;
  overflow-y: auto;
}

.matches-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 13px;
}

/* 表头 */
.matches-table thead th {
  background: var(--bg-secondary, #f9fafb);
  border-bottom: 2px solid var(--border-color, #e5e7eb);
  padding: 12px 8px;
  text-align: center;
  font-weight: 600;
  color: var(--text-primary, #111827);
  position: sticky;
  top: 0;
  z-index: 10;
}

.league-col { width: 15%; }
.time-col { width: 12%; }
.home-col { width: 20%; }
.score-col { width: 10%; }
.away-col { width: 20%; }
.odds-col { width: 12%; }
.report-col { width: 11%; }

.tooltip-icon {
  margin-left: 4px;
  color: var(--text-tertiary, #9ca3af);
  cursor: help;
}

/* 比赛行 */
.match-row {
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid var(--border-light, #f3f4f6);
}

.match-row:hover {
  background: var(--bg-hover, #f9fafb);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.match-row td {
  padding: 12px 8px;
  vertical-align: middle;
  text-align: center;
}

/* 队伍容器 - 对抗性布局 */
.team-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.home-team {
  justify-content: flex-end;
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.05));
  border-left: 3px solid #ef4444;
}

.away-team {
  justify-content: flex-start;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.05));
  border-right: 3px solid #3b82f6;
}

.team-name {
  color: var(--text-primary, #111827);
  font-weight: 600;
  font-size: 13px;
}

/* 比分样式 - 对抗性风格 */
.score {
  font-weight: 800;
  font-size: 16px;
  color: #dc2626;
  text-shadow: 0 1px 2px rgba(220, 38, 38, 0.2);
  letter-spacing: 1px;
}

.vs {
  color: var(--text-secondary, #6b7280);
  font-weight: 600;
  font-size: 14px;
  letter-spacing: 2px;
}

/* 指示器 */
.indicator-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.odds-indicator {
  color: #f59e0b;
  font-size: 8px;
  animation: pulse 2s infinite;
}

.report-indicator {
  color: #10b981;
  font-size: 14px;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 无比赛状态 */
.no-matches {
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  text-align: center;
  color: var(--text-secondary, #6b7280);
  width: auto;
  min-width: 300px;
  z-index: 10;
}

.no-matches-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: var(--text-tertiary, #9ca3af);
}

.no-matches h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 500;
  color: var(--text-primary, #111827);
}

.no-matches p {
  margin: 0;
  font-size: 14px;
}

/* 滚动条样式 */
.table-container::-webkit-scrollbar {
  width: 6px;
}

.table-container::-webkit-scrollbar-track {
  background: var(--bg-primary, #ffffff);
}

.table-container::-webkit-scrollbar-thumb {
  background: var(--border-color, #e5e7eb);
  border-radius: 3px;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary, #6b7280);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .matches-table {
    font-size: 12px;
  }

  .matches-table thead th,
  .match-row td {
    padding: 8px 4px;
  }

  .league-col { width: 18%; }
  .time-col { width: 15%; }
  .home-col { width: 22%; }
  .score-col { width: 10%; }
  .away-col { width: 22%; }
  .odds-col { width: 8%; }
  .report-col { width: 5%; }
}

/* 深色主题支持 */
.dark-theme .match-table-container {
  background: var(--bg-primary-dark, #111827);
}

.dark-theme .empty-state h3,
.dark-theme .no-matches h3 {
  color: var(--text-primary-dark, #f9fafb);
}

.dark-theme .content-header {
  background: var(--bg-secondary-dark, #1f2937);
  border-bottom-color: var(--border-color-dark, #374151);
}

.dark-theme .content-header h3 {
  color: var(--text-primary-dark, #f9fafb);
}

.dark-theme .match-count {
  background: var(--bg-tertiary-dark, #374151);
  color: var(--text-secondary-dark, #9ca3af);
}

.dark-theme .matches-table thead th {
  background: var(--bg-secondary-dark, #1f2937);
  border-bottom-color: var(--border-color-dark, #374151);
  color: var(--text-primary-dark, #f9fafb);
}

.dark-theme .match-row {
  border-bottom-color: var(--border-light-dark, #374151);
}

.dark-theme .match-row:hover {
  background: var(--bg-hover-dark, #374151);
}

.dark-theme .team-name {
  color: var(--text-primary-dark, #f9fafb);
}

.dark-theme .sort-button {
  background: var(--bg-secondary-dark, #1f2937);
  border-color: var(--border-color-dark, #374151);
  color: var(--text-secondary-dark, #9ca3af);
}

.dark-theme .sort-button:hover {
  background: var(--bg-hover-dark, #374151);
  border-color: var(--border-hover-dark, #4b5563);
  color: var(--text-primary-dark, #f9fafb);
}

.dark-theme .home-team {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.1));
}

.dark-theme .away-team {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(59, 130, 246, 0.1));
}

.dark-theme .vs {
  color: var(--text-secondary-dark, #9ca3af);
}
